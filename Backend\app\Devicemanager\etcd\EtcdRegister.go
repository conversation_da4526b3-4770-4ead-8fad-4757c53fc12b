package etcdregister

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"GCF/app/Devicemanager/internal/svc"
	"GCF/pkg/zetcd"

	"github.com/zeromicro/go-zero/core/logx"
	clientv3 "go.etcd.io/etcd/client/v3"
)




// DeviceEtcdManager 设备 etcd 管理器
type DeviceEtcdManager struct {
	svcCtx     *svc.ServiceContext
	etcdClient *zetcd.ZEtcdClient
}

// DeviceDataBuilder 设备数据构建器
type DeviceDataBuilder struct {
	TableFields    []string               // 数据表字段
	DeviceData     map[string]interface{} // 设备数据
	AdditionalData map[string]interface{} // 补充数据
	Format         string                 // 输出格式: "json", "kv", "csv"
}

// EtcdKeyConfig etcd 键配置
type EtcdKeyConfig struct {
	ServicePrefix string // 服务前缀，如 "device"
	DataType      string // 数据类型，如 "config", "status", "data"
	DeviceID      string // 设备ID
}







// NewDeviceEtcdManager 创建设备 etcd 管理器
func NewDeviceEtcdManager(svcCtx *svc.ServiceContext) *DeviceEtcdManager {
	return &DeviceEtcdManager{
		svcCtx:     svcCtx,
		etcdClient: svcCtx.ZEtcdClient,
	}
}

// RegisterDeviceService 注册设备服务到 etcd
func (dem *DeviceEtcdManager) RegisterDeviceService(serviceName, serviceAddr string) error {
	logx.Infof("Registering device service: %s at %s", serviceName, serviceAddr)

	// 使用 zetcd 包的 Publish 方法注册服务
	err := dem.etcdClient.Publish(serviceName, serviceAddr)
	if err != nil {
		logx.Errorf("Failed to register service: %v", err)
		return err
	}

	logx.Infof("Device service registered successfully")
	return nil
}

// BuildEtcdKey 构建 etcd 键
func (dem *DeviceEtcdManager) BuildEtcdKey(config *EtcdKeyConfig) string {
	return fmt.Sprintf("%s/%s/%s", config.ServicePrefix, config.DeviceID, config.DataType)
}

// WriteDeviceData 写入设备数据到 etcd
func (dem *DeviceEtcdManager) WriteDeviceData(deviceID string, tableFields []string, deviceData map[string]interface{}) error {
	// 构建数据字符串
	builder := &DeviceDataBuilder{
		TableFields:    tableFields,
		DeviceData:     deviceData,
		AdditionalData: make(map[string]interface{}),
		Format:         "json",
	}

	// 添加补充数据
	builder.AdditionalData["timestamp"] = time.Now().Unix()
	builder.AdditionalData["source"] = "device_manager"
	builder.AdditionalData["version"] = "v1.0"

	// 构建最终数据字符串
	dataString, err := dem.BuildDataString(builder)
	if err != nil {
		return err
	}

	// 构建 etcd 键
	keyConfig := &EtcdKeyConfig{
		ServicePrefix: "device",
		DeviceID:      deviceID,
		DataType:      "data",
	}
	key := dem.BuildEtcdKey(keyConfig)

	// 写入 etcd
	_, err = dem.etcdClient.Put(key, dataString)
	if err != nil {
		logx.Errorf("Failed to write device data to etcd: %v", err)
		return err
	}

	logx.Infof("Device data written to etcd: %s", key)
	return nil
}

// BuildDataString 构建数据字符串
func (dem *DeviceEtcdManager) BuildDataString(builder *DeviceDataBuilder) (string, error) {
	switch builder.Format {
	case "json":
		return dem.buildJSONString(builder)
	case "kv":
		return dem.buildKVString(builder)
	case "csv":
		return dem.buildCSVString(builder)
	default:
		return dem.buildJSONString(builder)
	}
}

// buildJSONString 构建 JSON 格式字符串
func (dem *DeviceEtcdManager) buildJSONString(builder *DeviceDataBuilder) (string, error) {
	result := make(map[string]interface{})

	// 添加表字段数据
	for _, field := range builder.TableFields {
		if value, exists := builder.DeviceData[field]; exists {
			result[field] = value
		}
	}

	// 添加补充数据
	for key, value := range builder.AdditionalData {
		result[key] = value
	}

	// 转换为 JSON 字符串
	jsonBytes, err := json.Marshal(result)
	if err != nil {
		return "", err
	}

	return string(jsonBytes), nil
}

// buildKVString 构建键值对格式字符串
func (dem *DeviceEtcdManager) buildKVString(builder *DeviceDataBuilder) (string, error) {
	var parts []string

	// 添加表字段数据
	for _, field := range builder.TableFields {
		if value, exists := builder.DeviceData[field]; exists {
			parts = append(parts, fmt.Sprintf("%s=%v", field, value))
		}
	}

	// 添加补充数据
	for key, value := range builder.AdditionalData {
		parts = append(parts, fmt.Sprintf("%s=%v", key, value))
	}

	return strings.Join(parts, "&"), nil
}

// buildCSVString 构建 CSV 格式字符串
func (dem *DeviceEtcdManager) buildCSVString(builder *DeviceDataBuilder) (string, error) {
	var values []string

	// 添加表字段数据
	for _, field := range builder.TableFields {
		if value, exists := builder.DeviceData[field]; exists {
			values = append(values, fmt.Sprintf("%v", value))
		} else {
			values = append(values, "")
		}
	}

	// 添加补充数据
	for _, value := range builder.AdditionalData {
		values = append(values, fmt.Sprintf("%v", value))
	}

	return strings.Join(values, ","), nil
}

// WriteDeviceConfig 写入设备配置到 etcd
func (dem *DeviceEtcdManager) WriteDeviceConfig(deviceID string, config map[string]interface{}) error {
	// 添加元数据
	configWithMeta := map[string]interface{}{
		"device_id": deviceID,
		"config":    config,
		"timestamp": time.Now().Unix(),
		"source":    "device_manager",
	}

	configJSON, err := json.Marshal(configWithMeta)
	if err != nil {
		return err
	}

	// 构建 etcd 键
	keyConfig := &EtcdKeyConfig{
		ServicePrefix: "device",
		DeviceID:      deviceID,
		DataType:      "config",
	}
	key := dem.BuildEtcdKey(keyConfig)

	// 写入 etcd
	_, err = dem.etcdClient.Put(key, string(configJSON))
	if err != nil {
		logx.Errorf("Failed to write device config to etcd: %v", err)
		return err
	}

	logx.Infof("Device config written to etcd: %s", key)
	return nil
}

// WriteDeviceStatus 写入设备状态到 etcd
func (dem *DeviceEtcdManager) WriteDeviceStatus(deviceID, status string) error {
	statusData := map[string]interface{}{
		"device_id": deviceID,
		"status":    status,
		"timestamp": time.Now().Unix(),
		"source":    "device_manager",
	}

	statusJSON, err := json.Marshal(statusData)
	if err != nil {
		return err
	}

	// 构建 etcd 键
	keyConfig := &EtcdKeyConfig{
		ServicePrefix: "device",
		DeviceID:      deviceID,
		DataType:      "status",
	}
	key := dem.BuildEtcdKey(keyConfig)

	// 写入 etcd
	_, err = dem.etcdClient.Put(key, string(statusJSON))
	if err != nil {
		logx.Errorf("Failed to write device status to etcd: %v", err)
		return err
	}

	logx.Infof("Device status written to etcd: %s", key)
	return nil
}

// ReadDeviceData 从 etcd 读取设备数据
func (dem *DeviceEtcdManager) ReadDeviceData(deviceID string) (map[string]interface{}, error) {
	keyConfig := &EtcdKeyConfig{
		ServicePrefix: "device",
		DeviceID:      deviceID,
		DataType:      "data",
	}
	key := dem.BuildEtcdKey(keyConfig)

	// 从 etcd 读取
	resp, err := dem.etcdClient.Get(key)
	if err != nil {
		return nil, err
	}

	if len(resp.Kvs) == 0 {
		return nil, fmt.Errorf("device data not found for device: %s", deviceID)
	}

	var data map[string]interface{}
	err = json.Unmarshal(resp.Kvs[0].Value, &data)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// BatchWriteDeviceData 批量写入设备数据
func (dem *DeviceEtcdManager) BatchWriteDeviceData(devices map[string]map[string]interface{}, tableFields []string) error {
	for deviceID, deviceData := range devices {
		err := dem.WriteDeviceData(deviceID, tableFields, deviceData)
		if err != nil {
			logx.Errorf("Failed to write data for device %s: %v", deviceID, err)
			return err
		}
	}

	logx.Infof("Batch write completed for %d devices", len(devices))
	return nil
}

// WatchDeviceChanges 监听设备变化
func (dem *DeviceEtcdManager) WatchDeviceChanges(deviceID string, callback func(string, string)) {
	keyConfig := &EtcdKeyConfig{
		ServicePrefix: "device",
		DeviceID:      deviceID,
		DataType:      "",
	}
	watchKey := fmt.Sprintf("%s/%s/", keyConfig.ServicePrefix, keyConfig.DeviceID)

	// 使用 etcd 的 Watch 功能
	watchChan := dem.etcdClient.Client.Watch(context.Background(), watchKey, clientv3.WithPrefix())

	go func() {
		for watchResp := range watchChan {
			for _, event := range watchResp.Events {
				key := string(event.Kv.Key)
				value := string(event.Kv.Value)
				callback(key, value)
			}
		}
	}()

	logx.Infof("Started watching device changes for: %s", deviceID)
}

// DeleteDeviceData 删除设备数据
func (dem *DeviceEtcdManager) DeleteDeviceData(deviceID string) error {
	keyConfig := &EtcdKeyConfig{
		ServicePrefix: "device",
		DeviceID:      deviceID,
		DataType:      "",
	}
	deleteKey := fmt.Sprintf("%s/%s/", keyConfig.ServicePrefix, keyConfig.DeviceID)

	// 删除设备相关的所有数据
	_, err := dem.etcdClient.Client.Delete(context.Background(), deleteKey, clientv3.WithPrefix())
	if err != nil {
		logx.Errorf("Failed to delete device data: %v", err)
		return err
	}

	logx.Infof("Device data deleted for: %s", deviceID)
	return nil
}
