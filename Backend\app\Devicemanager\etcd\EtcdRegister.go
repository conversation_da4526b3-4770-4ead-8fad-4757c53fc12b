package etcdregister

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"GCF/app/Devicemanager/internal/svc"
	"GCF/pkg/zetcd"

	"github.com/zeromicro/go-zero/core/logx"
	"go.etcd.io/etcd/client/v3"
)


// DeviceEtcdManager 设备 etcd 管理器
type DeviceEtcdManager struct {
	svcCtx     *svc.ServiceContext
	etcdClient *zetcd.ZEtcdClient
}




// NewDeviceEtcdManager 创建设备 etcd 管理器
func NewDeviceEtcdManager(svcCtx *svc.ServiceContext) *DeviceEtcdManager {
	return &DeviceEtcdManager{
		svcCtx:     svcCtx,
		etcdClient: svcCtx.ZEtcdClient,
	}
}



// RegisterDeviceService 注册设备服务到 etcd
func (dem *DeviceEtcdManager) RegisterDeviceService(serviceName, serviceAddr string) error {
	logx.Infof("Registering device service: %s at %s", serviceName, serviceAddr)

	// 使用 zetcd 包的 Publish 方法注册服务
	err := dem.etcdClient.Publish(serviceName, serviceAddr)
	if err != nil {
		logx.Errorf("Failed to register service: %v", err)
		return err
	}

	logx.Infof("Device service registered successfully")
	return nil
}




// WriteDeviceData 写入设备数据到 etcd（直接调用 zetcd）
func (dem *DeviceEtcdManager) WriteDeviceData(deviceID string, tableFields []string, deviceData map[string]interface{}) error {
	// 1. 直接拼接字段和数据
	var parts []string
	for _, field := range tableFields {
		if value, exists := deviceData[field]; exists {
			parts = append(parts, fmt.Sprintf("%s=%v", field, value))
		}
	}

	// 2. 添加补充数据
	parts = append(parts, fmt.Sprintf("timestamp=%d", time.Now().Unix()))
	parts = append(parts, "source=device_manager")
	parts = append(parts, "version=v1.0")

	// 3. 拼接成最终字符串
	dataString := strings.Join(parts, "&")

	// 4. 构建 etcd 键
	key := fmt.Sprintf("device/%s/data", deviceID)

	// 5. 直接调用 zetcd.Put 写入
	_, err := dem.etcdClient.Put(key, dataString)
	if err != nil {
		logx.Errorf("Failed to write device data to etcd: %v", err)
		return err
	}

	logx.Infof("Device data written to etcd: %s = %s", key, dataString)
	return nil
}





// WriteDeviceDataAsJSON 写入设备数据为 JSON 格式（直接调用 zetcd）
func (dem *DeviceEtcdManager) WriteDeviceDataAsJSON(deviceID string, tableFields []string, deviceData map[string]interface{}) error {
	// 1. 构建 JSON 数据
	result := make(map[string]interface{})

	// 添加表字段数据
	for _, field := range tableFields {
		if value, exists := deviceData[field]; exists {
			result[field] = value
		}
	}

	// 添加补充数据
	result["timestamp"] = time.Now().Unix()
	result["source"] = "device_manager"
	result["version"] = "v1.0"

	// 2. 转换为 JSON 字符串
	jsonBytes, err := json.Marshal(result)
	if err != nil {
		return err
	}

	// 3. 构建键并直接写入 etcd
	key := fmt.Sprintf("device/%s/data_json", deviceID)
	_, err = dem.etcdClient.Put(key, string(jsonBytes))
	if err != nil {
		logx.Errorf("Failed to write JSON data to etcd: %v", err)
		return err
	}

	logx.Infof("JSON data written to etcd: %s", key)
	return nil
}

// WriteDeviceConfig 写入设备配置到 etcd（直接调用 zetcd）
func (dem *DeviceEtcdManager) WriteDeviceConfig(deviceID string, config map[string]interface{}) error {
	// 1. 添加元数据
	configWithMeta := map[string]interface{}{
		"device_id": deviceID,
		"config":    config,
		"timestamp": time.Now().Unix(),
		"source":    "device_manager",
	}

	// 2. 转换为 JSON
	configJSON, err := json.Marshal(configWithMeta)
	if err != nil {
		return err
	}

	// 3. 构建键并直接写入 etcd
	key := fmt.Sprintf("device/%s/config", deviceID)
	_, err = dem.etcdClient.Put(key, string(configJSON))
	if err != nil {
		logx.Errorf("Failed to write device config to etcd: %v", err)
		return err
	}

	logx.Infof("Device config written to etcd: %s", key)
	return nil
}

// WriteDeviceStatus 写入设备状态到 etcd（直接调用 zetcd）
func (dem *DeviceEtcdManager) WriteDeviceStatus(deviceID, status string) error {
	// 1. 构建状态数据
	statusData := map[string]interface{}{
		"device_id": deviceID,
		"status":    status,
		"timestamp": time.Now().Unix(),
		"source":    "device_manager",
	}

	// 2. 转换为 JSON
	statusJSON, err := json.Marshal(statusData)
	if err != nil {
		return err
	}

	// 3. 构建键并直接写入 etcd
	key := fmt.Sprintf("device/%s/status", deviceID)
	_, err = dem.etcdClient.Put(key, string(statusJSON))
	if err != nil {
		logx.Errorf("Failed to write device status to etcd: %v", err)
		return err
	}

	logx.Infof("Device status written to etcd: %s", key)
	return nil
}

// ReadDeviceData 从 etcd 读取设备数据（直接调用 zetcd）
func (dem *DeviceEtcdManager) ReadDeviceData(deviceID string) (string, error) {
	// 1. 构建键
	key := fmt.Sprintf("device/%s/data", deviceID)

	// 2. 直接调用 zetcd.Get 读取
	resp, err := dem.etcdClient.Get(key)
	if err != nil {
		return "", err
	}

	if len(resp.Kvs) == 0 {
		return "", fmt.Errorf("device data not found for device: %s", deviceID)
	}

	value := string(resp.Kvs[0].Value)
	logx.Infof("Device data read from etcd: %s = %s", key, value)
	return value, nil
}

// DirectWriteToEtcd 直接调用 zetcd 方法写入数据
func (dem *DeviceEtcdManager) DirectWriteToEtcd(key, value string) error {
	// 直接调用 zetcd 的 Put 方法
	_, err := dem.etcdClient.Put(key, value)
	if err != nil {
		logx.Errorf("Failed to write to etcd: %v", err)
		return err
	}

	logx.Infof("Data written to etcd: %s = %s", key, value)
	return nil
}



