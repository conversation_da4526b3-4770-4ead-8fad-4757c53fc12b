package etcdregister

import (
	"fmt"
	"log"

	"GCF/app/Devicemanager/internal/svc"
)

// UpdatedUsageExample 展示更新后的直接调用 zetcd 方法
func UpdatedUsageExample(svcCtx *svc.ServiceContext) {
	// 创建设备 etcd 管理器
	etcdManager := NewDeviceEtcdManager(svcCtx)

	fmt.Println("=== 更新后的直接 zetcd 调用示例 ===")

	// 1. 注册设备服务（直接调用 zetcd.Publish）
	fmt.Println("\n1. 服务注册")
	err := etcdManager.RegisterDeviceService("device-manager", "localhost:8080")
	if err != nil {
		log.Printf("服务注册失败: %v", err)
	} else {
		fmt.Println("✅ 服务注册成功")
	}

	// 2. 写入设备数据（直接调用 zetcd.Put，KV 格式）
	fmt.Println("\n2. 写入设备数据（KV 格式）")
	deviceID := "device001"
	tableFields := []string{"device_id", "name", "temperature", "humidity", "status"}
	deviceData := map[string]interface{}{
		"device_id":   deviceID,
		"name":        "Temperature Sensor 1",
		"temperature": 25.5,
		"humidity":    60.2,
		"status":      "active",
	}

	err = etcdManager.WriteDeviceData(deviceID, tableFields, deviceData)
	if err != nil {
		log.Printf("设备数据写入失败: %v", err)
	} else {
		fmt.Println("✅ 设备数据写入成功（KV 格式）")
	}

	// 3. 写入设备数据（JSON 格式）
	fmt.Println("\n3. 写入设备数据（JSON 格式）")
	err = etcdManager.WriteDeviceDataAsJSON(deviceID, tableFields, deviceData)
	if err != nil {
		log.Printf("JSON 数据写入失败: %v", err)
	} else {
		fmt.Println("✅ JSON 数据写入成功")
	}

	// 4. 写入设备数据（CSV 格式）
	fmt.Println("\n4. 写入设备数据（CSV 格式）")
	err = etcdManager.WriteDeviceDataAsCSV(deviceID, tableFields, deviceData)
	if err != nil {
		log.Printf("CSV 数据写入失败: %v", err)
	} else {
		fmt.Println("✅ CSV 数据写入成功")
	}

	// 5. 写入设备配置（直接调用 zetcd.Put）
	fmt.Println("\n5. 写入设备配置")
	deviceConfig := map[string]interface{}{
		"sampling_rate": 1000,
		"threshold":     30.0,
		"enabled":       true,
		"location":      "building_a_room_101",
	}

	err = etcdManager.WriteDeviceConfig(deviceID, deviceConfig)
	if err != nil {
		log.Printf("设备配置写入失败: %v", err)
	} else {
		fmt.Println("✅ 设备配置写入成功")
	}

	// 6. 更新设备状态（直接调用 zetcd.Put）
	fmt.Println("\n6. 更新设备状态")
	err = etcdManager.WriteDeviceStatus(deviceID, "online")
	if err != nil {
		log.Printf("设备状态更新失败: %v", err)
	} else {
		fmt.Println("✅ 设备状态更新成功")
	}

	// 7. 读取设备数据（直接调用 zetcd.Get）
	fmt.Println("\n7. 读取设备数据")
	data, err := etcdManager.ReadDeviceData(deviceID)
	if err != nil {
		log.Printf("设备数据读取失败: %v", err)
	} else {
		fmt.Printf("✅ 设备数据: %s\n", data)
	}

	// 8. 直接写入任意数据（最简单的方式）
	fmt.Println("\n8. 直接写入任意数据")
	err = etcdManager.DirectWriteToEtcd("test/simple", "hello world")
	if err != nil {
		log.Printf("直接写入失败: %v", err)
	} else {
		fmt.Println("✅ 直接写入成功")
	}

	// 9. 直接读取任意数据
	fmt.Println("\n9. 直接读取任意数据")
	value, err := etcdManager.DirectReadFromEtcd("test/simple")
	if err != nil {
		log.Printf("直接读取失败: %v", err)
	} else {
		fmt.Printf("✅ 直接读取成功: %s\n", value)
	}

	// 10. 批量写入设备数据
	fmt.Println("\n10. 批量写入设备数据")
	batchDevices := map[string]map[string]interface{}{
		"device002": {
			"device_id":   "device002",
			"name":        "Humidity Sensor 1",
			"temperature": 22.3,
			"humidity":    55.8,
			"status":      "active",
		},
		"device003": {
			"device_id":   "device003",
			"name":        "Pressure Sensor 1",
			"temperature": 24.1,
			"humidity":    58.5,
			"status":      "active",
		},
	}

	err = etcdManager.BatchWriteDeviceData(batchDevices, tableFields)
	if err != nil {
		log.Printf("批量写入失败: %v", err)
	} else {
		fmt.Println("✅ 批量写入成功")
	}

	// 11. 监听设备变化
	fmt.Println("\n11. 启动设备监听")
	etcdManager.WatchDeviceChanges(deviceID, func(key, value string) {
		fmt.Printf("📡 设备变化检测: %s = %s\n", key, value)
	})
	fmt.Println("✅ 设备监听已启动")

	fmt.Println("\n=== 所有操作完成！===")
}

// SimpleFieldConcatenationExample 简单的字段拼接示例
func SimpleFieldConcatenationExample(svcCtx *svc.ServiceContext) {
	etcdManager := NewDeviceEtcdManager(svcCtx)

	fmt.Println("\n=== 简单字段拼接示例 ===")

	// 模拟从数据表提取的字段
	tableFields := []string{"id", "name", "type", "location", "status"}

	// 模拟设备数据
	deviceData := map[string]interface{}{
		"id":       "dev001",
		"name":     "Temperature Sensor",
		"type":     "sensor",
		"location": "building_a",
		"status":   "active",
	}

	// 使用简化的写入方法（直接调用 zetcd）
	err := etcdManager.WriteDeviceData("dev001", tableFields, deviceData)
	if err != nil {
		log.Printf("字段拼接写入失败: %v", err)
	} else {
		fmt.Println("✅ 字段拼接写入成功")
	}

	// 读取验证
	result, err := etcdManager.ReadDeviceData("dev001")
	if err != nil {
		log.Printf("读取失败: %v", err)
	} else {
		fmt.Printf("✅ 拼接结果: %s\n", result)
	}
}

// CompareDirectZetcdCalls 对比不同的直接 zetcd 调用方式
func CompareDirectZetcdCalls(svcCtx *svc.ServiceContext) {
	etcdManager := NewDeviceEtcdManager(svcCtx)

	fmt.Println("\n=== 直接 zetcd 调用方式对比 ===")

	// 方式1: 通过封装的 DirectWriteToEtcd
	fmt.Println("方式1: 封装的直接写入")
	err1 := etcdManager.DirectWriteToEtcd("test/method1", "封装方法")
	if err1 == nil {
		fmt.Println("✅ 封装方法成功")
	}

	// 方式2: 直接调用 zetcd.Put
	fmt.Println("方式2: 直接调用 zetcd.Put")
	_, err2 := etcdManager.etcdClient.Put("test/method2", "直接调用")
	if err2 == nil {
		fmt.Println("✅ 直接调用成功")
	}

	// 方式3: 使用 zetcd.Publish（服务注册）
	fmt.Println("方式3: 使用 zetcd.Publish")
	err3 := etcdManager.etcdClient.Publish("test-service", "localhost:9999")
	if err3 == nil {
		fmt.Println("✅ Publish 成功")
	}

	fmt.Println("\n所有方式都是直接调用 zetcd 的方法！")
	fmt.Println("- DirectWriteToEtcd: 封装了 zetcd.Put")
	fmt.Println("- etcdClient.Put: 直接调用 zetcd.Put")
	fmt.Println("- etcdClient.Publish: 直接调用 zetcd.Publish")
}
