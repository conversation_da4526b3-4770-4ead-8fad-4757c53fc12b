package etcdregister

import (
	"context"
	"fmt"
	"testing"
	"time"

	"GCF/app/Devicemanager/internal/config"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/pkg/zetcd"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 测试配置
var testConfig = config.Config{
	ZEtcdConf: zetcd.ZEtcdConf{
		Endpoints: []string{"127.0.0.1:2379"},
		ID:        12345,
	},
}

// setupTestManager 创建测试用的 etcd 管理器
func setupTestManager(t *testing.T) *DeviceEtcdManager {
	svcCtx := svc.NewServiceContext(testConfig)
	require.NotNil(t, svcCtx, "服务上下文创建失败")
	require.NotNil(t, svcCtx.ZEtcdClient, "etcd 客户端创建失败")

	manager := NewDeviceEtcdManager(svcCtx)
	require.NotNil(t, manager, "etcd 管理器创建失败")

	return manager
}

// TestNewDeviceEtcdManager 测试管理器创建
func TestNewDeviceEtcdManager(t *testing.T) {
	manager := setupTestManager(t)
	
	assert.NotNil(t, manager.svcCtx, "服务上下文不应为空")
	assert.NotNil(t, manager.etcdClient, "etcd 客户端不应为空")
	
	fmt.Println("✅ 管理器创建测试通过")
}

// TestRegisterDeviceService 测试服务注册
func TestRegisterDeviceService(t *testing.T) {
	manager := setupTestManager(t)
	
	serviceName := "test-device-manager"
	serviceAddr := "localhost:8080"
	
	err := manager.RegisterDeviceService(serviceName, serviceAddr)
	assert.NoError(t, err, "服务注册应该成功")
	
	fmt.Printf("✅ 服务注册测试通过: %s -> %s\n", serviceName, serviceAddr)
}

// TestDirectWriteAndRead 测试直接写入和读取
func TestDirectWriteAndRead(t *testing.T) {
	manager := setupTestManager(t)
	
	testKey := "test/direct/key"
	testValue := "test direct value"
	
	// 测试写入
	err := manager.DirectWriteToEtcd(testKey, testValue)
	assert.NoError(t, err, "直接写入应该成功")
	
	// 测试读取
	value, err := manager.DirectReadFromEtcd(testKey)
	assert.NoError(t, err, "直接读取应该成功")
	assert.Equal(t, testValue, value, "读取的值应该与写入的值相同")
	
	fmt.Printf("✅ 直接写入读取测试通过: %s = %s\n", testKey, value)
}

// TestWriteDeviceData 测试设备数据写入（您的核心需求）
func TestWriteDeviceData(t *testing.T) {
	manager := setupTestManager(t)
	
	deviceID := "test_device_001"
	tableFields := []string{"device_id", "name", "temperature", "humidity", "status"}
	deviceData := map[string]interface{}{
		"device_id":   deviceID,
		"name":        "Test Temperature Sensor",
		"temperature": 25.5,
		"humidity":    60.2,
		"status":      "active",
	}
	
	// 测试写入设备数据
	err := manager.WriteDeviceData(deviceID, tableFields, deviceData)
	assert.NoError(t, err, "设备数据写入应该成功")
	
	// 验证数据是否正确写入
	key := fmt.Sprintf("device/%s/data", deviceID)
	value, err := manager.DirectReadFromEtcd(key)
	assert.NoError(t, err, "读取设备数据应该成功")
	assert.Contains(t, value, "device_id=test_device_001", "应该包含设备ID")
	assert.Contains(t, value, "temperature=25.5", "应该包含温度数据")
	assert.Contains(t, value, "humidity=60.2", "应该包含湿度数据")
	assert.Contains(t, value, "timestamp=", "应该包含时间戳")
	assert.Contains(t, value, "source=device_manager", "应该包含数据源")
	
	fmt.Printf("✅ 设备数据写入测试通过: %s\n", value)
}

// TestWriteDeviceDataAsJSON 测试 JSON 格式写入
func TestWriteDeviceDataAsJSON(t *testing.T) {
	manager := setupTestManager(t)
	
	deviceID := "test_device_json"
	tableFields := []string{"device_id", "temperature", "humidity"}
	deviceData := map[string]interface{}{
		"device_id":   deviceID,
		"temperature": 22.3,
		"humidity":    55.8,
	}
	
	// 测试 JSON 格式写入
	err := manager.WriteDeviceDataAsJSON(deviceID, tableFields, deviceData)
	assert.NoError(t, err, "JSON 格式写入应该成功")
	
	// 验证 JSON 数据
	key := fmt.Sprintf("device/%s/data_json", deviceID)
	value, err := manager.DirectReadFromEtcd(key)
	assert.NoError(t, err, "读取 JSON 数据应该成功")
	assert.Contains(t, value, `"device_id":"test_device_json"`, "应该包含 JSON 格式的设备ID")
	assert.Contains(t, value, `"temperature":22.3`, "应该包含 JSON 格式的温度")
	
	fmt.Printf("✅ JSON 格式写入测试通过: %s\n", value)
}

// TestWriteDeviceDataAsCSV 测试 CSV 格式写入
func TestWriteDeviceDataAsCSV(t *testing.T) {
	manager := setupTestManager(t)
	
	deviceID := "test_device_csv"
	tableFields := []string{"device_id", "temperature", "humidity"}
	deviceData := map[string]interface{}{
		"device_id":   deviceID,
		"temperature": 27.1,
		"humidity":    65.4,
	}
	
	// 测试 CSV 格式写入
	err := manager.WriteDeviceDataAsCSV(deviceID, tableFields, deviceData)
	assert.NoError(t, err, "CSV 格式写入应该成功")
	
	// 验证 CSV 数据
	key := fmt.Sprintf("device/%s/data_csv", deviceID)
	value, err := manager.DirectReadFromEtcd(key)
	assert.NoError(t, err, "读取 CSV 数据应该成功")
	assert.Contains(t, value, "test_device_csv", "应该包含设备ID")
	assert.Contains(t, value, "27.1", "应该包含温度值")
	assert.Contains(t, value, "65.4", "应该包含湿度值")
	
	fmt.Printf("✅ CSV 格式写入测试通过: %s\n", value)
}

// TestWriteDeviceConfig 测试设备配置写入
func TestWriteDeviceConfig(t *testing.T) {
	manager := setupTestManager(t)
	
	deviceID := "test_device_config"
	config := map[string]interface{}{
		"sampling_rate": 1000,
		"threshold":     30.0,
		"enabled":       true,
		"location":      "test_building",
	}
	
	// 测试配置写入
	err := manager.WriteDeviceConfig(deviceID, config)
	assert.NoError(t, err, "设备配置写入应该成功")
	
	// 验证配置数据
	key := fmt.Sprintf("device/%s/config", deviceID)
	value, err := manager.DirectReadFromEtcd(key)
	assert.NoError(t, err, "读取设备配置应该成功")
	assert.Contains(t, value, `"sampling_rate":1000`, "应该包含采样率配置")
	assert.Contains(t, value, `"enabled":true`, "应该包含启用状态")
	
	fmt.Printf("✅ 设备配置写入测试通过: %s\n", value)
}

// TestWriteDeviceStatus 测试设备状态写入
func TestWriteDeviceStatus(t *testing.T) {
	manager := setupTestManager(t)
	
	deviceID := "test_device_status"
	status := "online"
	
	// 测试状态写入
	err := manager.WriteDeviceStatus(deviceID, status)
	assert.NoError(t, err, "设备状态写入应该成功")
	
	// 验证状态数据
	key := fmt.Sprintf("device/%s/status", deviceID)
	value, err := manager.DirectReadFromEtcd(key)
	assert.NoError(t, err, "读取设备状态应该成功")
	assert.Contains(t, value, `"status":"online"`, "应该包含在线状态")
	assert.Contains(t, value, fmt.Sprintf(`"device_id":"%s"`, deviceID), "应该包含设备ID")
	
	fmt.Printf("✅ 设备状态写入测试通过: %s\n", value)
}

// TestBatchWriteDeviceData 测试批量写入
func TestBatchWriteDeviceData(t *testing.T) {
	manager := setupTestManager(t)
	
	tableFields := []string{"device_id", "temperature", "humidity"}
	devices := map[string]map[string]interface{}{
		"batch_device_001": {
			"device_id":   "batch_device_001",
			"temperature": 20.1,
			"humidity":    50.1,
		},
		"batch_device_002": {
			"device_id":   "batch_device_002",
			"temperature": 21.2,
			"humidity":    51.2,
		},
		"batch_device_003": {
			"device_id":   "batch_device_003",
			"temperature": 22.3,
			"humidity":    52.3,
		},
	}
	
	// 测试批量写入
	err := manager.BatchWriteDeviceData(devices, tableFields)
	assert.NoError(t, err, "批量写入应该成功")
	
	// 验证每个设备的数据
	for deviceID := range devices {
		key := fmt.Sprintf("device/%s/data", deviceID)
		value, err := manager.DirectReadFromEtcd(key)
		assert.NoError(t, err, fmt.Sprintf("读取设备 %s 数据应该成功", deviceID))
		assert.Contains(t, value, deviceID, fmt.Sprintf("应该包含设备ID %s", deviceID))
	}
	
	fmt.Printf("✅ 批量写入测试通过，成功写入 %d 个设备\n", len(devices))
}

// TestReadDeviceData 测试设备数据读取
func TestReadDeviceData(t *testing.T) {
	manager := setupTestManager(t)
	
	// 先写入测试数据
	deviceID := "test_read_device"
	testData := "device_id=test_read_device&temperature=25.0&humidity=60.0&timestamp=1234567890"
	key := fmt.Sprintf("device/%s/data", deviceID)
	
	err := manager.DirectWriteToEtcd(key, testData)
	require.NoError(t, err, "写入测试数据应该成功")
	
	// 测试读取
	value, err := manager.ReadDeviceData(deviceID)
	assert.NoError(t, err, "读取设备数据应该成功")
	assert.Equal(t, testData, value, "读取的数据应该与写入的数据相同")
	
	fmt.Printf("✅ 设备数据读取测试通过: %s\n", value)
}
