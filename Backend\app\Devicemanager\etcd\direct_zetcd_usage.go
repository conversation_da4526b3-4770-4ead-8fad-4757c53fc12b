package etcdregister

import (
	"fmt"
	"log"
	"strings"
	"time"

	"GCF/app/Devicemanager/internal/svc"
)

// DirectZetcdUsage 展示如何直接调用 zetcd 方法
func DirectZetcdUsage(svcCtx *svc.ServiceContext) {
	// 创建管理器
	etcdManager := NewDeviceEtcdManager(svcCtx)

	fmt.Println("=== 直接调用 zetcd 方法示例 ===")

	// 1. 直接调用 zetcd.Publish 注册服务
	fmt.Println("\n1. 服务注册（直接调用 zetcd.Publish）")
	err := etcdManager.PublishService("device-manager", "localhost:8080")
	if err != nil {
		log.Printf("服务注册失败: %v", err)
	} else {
		fmt.Println("✅ 服务注册成功")
	}

	// 2. 直接调用 zetcd.Put 写入简单数据
	fmt.Println("\n2. 写入简单数据（直接调用 zetcd.Put）")
	err = etcdManager.DirectWriteToEtcd("test/simple", "hello world")
	if err != nil {
		log.Printf("写入失败: %v", err)
	} else {
		fmt.Println("✅ 简单数据写入成功")
	}

	// 3. 直接调用 zetcd.Get 读取数据
	fmt.Println("\n3. 读取数据（直接调用 zetcd.Get）")
	value, err := etcdManager.DirectReadFromEtcd("test/simple")
	if err != nil {
		log.Printf("读取失败: %v", err)
	} else {
		fmt.Printf("✅ 读取成功: %s\n", value)
	}

	// 4. 直接调用 zetcd 写入设备数据（简化版）
	fmt.Println("\n4. 写入设备数据（直接调用 zetcd，无复杂构建器）")
	deviceID := "device001"
	tableFields := []string{"device_id", "name", "temperature", "humidity"}
	deviceData := map[string]interface{}{
		"device_id":   deviceID,
		"name":        "Temperature Sensor 1",
		"temperature": 25.5,
		"humidity":    60.2,
	}

	err = etcdManager.SimpleWriteDeviceData(deviceID, tableFields, deviceData)
	if err != nil {
		log.Printf("设备数据写入失败: %v", err)
	} else {
		fmt.Println("✅ 设备数据写入成功")
	}

	// 5. 读取刚写入的设备数据
	fmt.Println("\n5. 读取设备数据")
	deviceKey := fmt.Sprintf("device/%s/data", deviceID)
	deviceValue, err := etcdManager.DirectReadFromEtcd(deviceKey)
	if err != nil {
		log.Printf("设备数据读取失败: %v", err)
	} else {
		fmt.Printf("✅ 设备数据: %s\n", deviceValue)
	}
}

// QuickWriteExample 快速写入示例
func QuickWriteExample(svcCtx *svc.ServiceContext) {
	etcdManager := NewDeviceEtcdManager(svcCtx)

	// 直接写入各种类型的数据
	examples := map[string]string{
		"config/database":    "host=localhost;port=5432;db=device_db",
		"status/system":      "running",
		"device/dev001/temp": "25.5",
		"device/dev001/hum":  "60.2",
		"alert/threshold":    "30.0",
	}

	fmt.Println("\n=== 快速写入示例 ===")
	for key, value := range examples {
		err := etcdManager.DirectWriteToEtcd(key, value)
		if err != nil {
			log.Printf("写入失败 %s: %v", key, err)
		} else {
			fmt.Printf("✅ 写入成功: %s = %s\n", key, value)
		}
	}
}

// ManualFieldConcatenation 手动字段拼接示例（您的需求）
func ManualFieldConcatenation(svcCtx *svc.ServiceContext) {
	etcdManager := NewDeviceEtcdManager(svcCtx)

	fmt.Println("\n=== 手动字段拼接示例 ===")

	// 模拟从数据表提取的字段
	tableFields := []string{"id", "name", "type", "location", "status"}

	// 模拟设备数据
	deviceData := map[string]interface{}{
		"id":       "dev001",
		"name":     "Temperature Sensor",
		"type":     "sensor",
		"location": "building_a",
		"status":   "active",
	}

	// 手动拼接字段和数据
	var fieldParts []string
	for _, field := range tableFields {
		if value, exists := deviceData[field]; exists {
			fieldParts = append(fieldParts, fmt.Sprintf("%s:%v", field, value))
		}
	}

	// 添加您的补充数据
	additionalData := map[string]interface{}{
		"timestamp":    time.Now().Unix(),
		"source":       "device_manager",
		"version":      "v1.0",
		"processed_by": "etcd_register",
	}

	for key, value := range additionalData {
		fieldParts = append(fieldParts, fmt.Sprintf("%s:%v", key, value))
	}

	// 拼接成最终字符串
	finalString := fmt.Sprintf("[%s]", strings.Join(fieldParts, "|"))

	// 直接调用 zetcd.Put 写入
	key := "device/dev001/complete_data"
	err := etcdManager.DirectWriteToEtcd(key, finalString)
	if err != nil {
		log.Printf("拼接数据写入失败: %v", err)
	} else {
		fmt.Printf("✅ 拼接数据写入成功:\n")
		fmt.Printf("   键: %s\n", key)
		fmt.Printf("   值: %s\n", finalString)
	}
}

// BatchDirectWrite 批量直接写入
func BatchDirectWrite(svcCtx *svc.ServiceContext) {
	etcdManager := NewDeviceEtcdManager(svcCtx)

	fmt.Println("\n=== 批量直接写入示例 ===")

	// 批量设备数据
	devices := []map[string]interface{}{
		{"id": "dev001", "temp": 25.5, "hum": 60.2},
		{"id": "dev002", "temp": 22.3, "hum": 55.8},
		{"id": "dev003", "temp": 27.1, "hum": 65.4},
	}

	for i, device := range devices {
		// 构建数据字符串
		dataString := fmt.Sprintf("temp=%.1f&hum=%.1f&timestamp=%d",
			device["temp"], device["hum"], time.Now().Unix())

		// 构建键
		key := fmt.Sprintf("device/%s/sensor_data", device["id"])

		// 直接写入
		err := etcdManager.DirectWriteToEtcd(key, dataString)
		if err != nil {
			log.Printf("设备 %s 写入失败: %v", device["id"], err)
		} else {
			fmt.Printf("✅ 设备 %d 写入成功: %s\n", i+1, device["id"])
		}
	}
}

// CompareWithZetcdMethods 对比 zetcd 原生方法调用
func CompareWithZetcdMethods(svcCtx *svc.ServiceContext) {
	etcdManager := NewDeviceEtcdManager(svcCtx)

	fmt.Println("\n=== zetcd 原生方法对比 ===")

	// 方式1: 通过封装方法
	fmt.Println("方式1: 通过封装方法")
	err1 := etcdManager.DirectWriteToEtcd("test/method1", "封装方法")
	if err1 == nil {
		fmt.Println("✅ 封装方法写入成功")
	}

	// 方式2: 直接调用 zetcd 客户端
	fmt.Println("方式2: 直接调用 zetcd 客户端")
	_, err2 := etcdManager.etcdClient.Put("test/method2", "直接调用")
	if err2 == nil {
		fmt.Println("✅ 直接调用写入成功")
	}

	// 方式3: 使用 zetcd 的 Publish（服务注册）
	fmt.Println("方式3: 使用 zetcd 的 Publish")
	err3 := etcdManager.etcdClient.Publish("test-service", "localhost:9999")
	if err3 == nil {
		fmt.Println("✅ Publish 调用成功")
	}

	fmt.Println("\n所有方式都是直接调用 zetcd 的方法！")
}
