package EtcdRegister

import (
	"fmt"
	"testing"

	"GCF/app/Devicemanager/internal/config"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/pkg/zetcd"
)

// 基准测试配置
var benchConfig = config.Config{
	ZEtcdConf: zetcd.ZEtcdConf{
		Endpoints: []string{"127.0.0.1:2379"},
		ID:        12345,
	},
}

// setupBenchManager 创建基准测试用的 etcd 管理器
func setupBenchManager() *DeviceEtcdManager {
	svcCtx := svc.NewServiceContext(benchConfig)
	return NewDeviceEtcdManager(svcCtx)
}

// BenchmarkDirectWriteToEtcd 基准测试直接写入
func BenchmarkDirectWriteToEtcd(b *testing.B) {
	manager := setupBenchManager()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		key := fmt.Sprintf("bench/direct/key_%d", i)
		value := fmt.Sprintf("bench_value_%d", i)

		err := manager.DirectWriteToEtcd(key, value)
		if err != nil {
			b.Fatalf("写入失败: %v", err)
		}
	}
}

// BenchmarkWriteDeviceData 基准测试设备数据写入
func BenchmarkWriteDeviceData(b *testing.B) {
	manager := setupBenchManager()

	tableFields := []string{"device_id", "temperature", "humidity", "status"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		deviceID := fmt.Sprintf("bench_device_%d", i)
		deviceData := map[string]interface{}{
			"device_id":   deviceID,
			"temperature": 25.5 + float64(i%10),
			"humidity":    60.2 + float64(i%20),
			"status":      "active",
		}

		err := manager.WriteDeviceData(deviceID, tableFields, deviceData)
		if err != nil {
			b.Fatalf("设备数据写入失败: %v", err)
		}
	}
}

// BenchmarkWriteDeviceDataAsJSON 基准测试 JSON 格式写入
func BenchmarkWriteDeviceDataAsJSON(b *testing.B) {
	manager := setupBenchManager()

	tableFields := []string{"device_id", "temperature", "humidity", "status"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		deviceID := fmt.Sprintf("bench_json_device_%d", i)
		deviceData := map[string]interface{}{
			"device_id":   deviceID,
			"temperature": 25.5 + float64(i%10),
			"humidity":    60.2 + float64(i%20),
			"status":      "active",
		}

		err := manager.WriteDeviceDataAsJSON(deviceID, tableFields, deviceData)
		if err != nil {
			b.Fatalf("JSON 格式写入失败: %v", err)
		}
	}
}

// BenchmarkDirectReadFromEtcd 基准测试直接读取
func BenchmarkDirectReadFromEtcd(b *testing.B) {
	manager := setupBenchManager()

	// 预先写入测试数据
	testKey := "bench/read/test_key"
	testValue := "bench_test_value"
	err := manager.DirectWriteToEtcd(testKey, testValue)
	if err != nil {
		b.Fatalf("预写入失败: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		resp, err := manager.etcdClient.Get(testKey)
		if err != nil {
			b.Fatalf("读取失败: %v", err)
		}
		if len(resp.Kvs) == 0 {
			b.Fatalf("键不存在: %s", testKey)
		}
	}
}

// BenchmarkWriteDeviceConfig 基准测试设备配置写入
func BenchmarkWriteDeviceConfig(b *testing.B) {
	manager := setupBenchManager()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		deviceID := fmt.Sprintf("bench_config_device_%d", i)
		config := map[string]interface{}{
			"sampling_rate": 1000 + i%500,
			"threshold":     30.0 + float64(i%10),
			"enabled":       i%2 == 0,
			"location":      fmt.Sprintf("building_%d", i%5),
		}

		err := manager.WriteDeviceConfig(deviceID, config)
		if err != nil {
			b.Fatalf("设备配置写入失败: %v", err)
		}
	}
}

// BenchmarkWriteDeviceStatus 基准测试设备状态写入
func BenchmarkWriteDeviceStatus(b *testing.B) {
	manager := setupBenchManager()

	statuses := []string{"online", "offline", "error", "maintenance"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		deviceID := fmt.Sprintf("bench_status_device_%d", i)
		status := statuses[i%len(statuses)]

		err := manager.WriteDeviceStatus(deviceID, status)
		if err != nil {
			b.Fatalf("设备状态写入失败: %v", err)
		}
	}
}

// BenchmarkBatchWriteDeviceData 基准测试批量写入
func BenchmarkBatchWriteDeviceData(b *testing.B) {
	manager := setupBenchManager()

	tableFields := []string{"device_id", "temperature", "humidity", "status"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 每次批量写入 10 个设备
		devices := make(map[string]map[string]interface{})
		for j := 0; j < 10; j++ {
			deviceID := fmt.Sprintf("bench_batch_device_%d_%d", i, j)
			devices[deviceID] = map[string]interface{}{
				"device_id":   deviceID,
				"temperature": 25.5 + float64(j),
				"humidity":    60.2 + float64(j),
				"status":      "active",
			}
		}

		err := manager.BatchWriteDeviceData(devices, tableFields)
		if err != nil {
			b.Fatalf("批量写入失败: %v", err)
		}
	}
}

// BenchmarkRegisterDeviceService 基准测试服务注册
func BenchmarkRegisterDeviceService(b *testing.B) {
	manager := setupBenchManager()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		serviceName := fmt.Sprintf("bench-service-%d", i)
		serviceAddr := fmt.Sprintf("localhost:%d", 8000+i%1000)

		err := manager.RegisterDeviceService(serviceName, serviceAddr)
		if err != nil {
			b.Fatalf("服务注册失败: %v", err)
		}
	}
}

// BenchmarkReadDeviceData 基准测试设备数据读取
func BenchmarkReadDeviceData(b *testing.B) {
	manager := setupBenchManager()

	// 预先写入测试数据
	deviceID := "bench_read_device"
	tableFields := []string{"device_id", "temperature", "humidity"}
	deviceData := map[string]interface{}{
		"device_id":   deviceID,
		"temperature": 25.5,
		"humidity":    60.2,
	}

	err := manager.WriteDeviceData(deviceID, tableFields, deviceData)
	if err != nil {
		b.Fatalf("预写入设备数据失败: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := manager.ReadDeviceData(deviceID)
		if err != nil {
			b.Fatalf("读取设备数据失败: %v", err)
		}
	}
}

// BenchmarkCompareFormats 对比不同格式的性能
func BenchmarkCompareFormats(b *testing.B) {
	manager := setupBenchManager()
	tableFields := []string{"device_id", "temperature", "humidity", "status"}
	deviceData := map[string]interface{}{
		"device_id":   "compare_device",
		"temperature": 25.5,
		"humidity":    60.2,
		"status":      "active",
	}

	b.Run("KV格式", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			deviceID := fmt.Sprintf("kv_device_%d", i)
			err := manager.WriteDeviceData(deviceID, tableFields, deviceData)
			if err != nil {
				b.Fatalf("KV 格式写入失败: %v", err)
			}
		}
	})

	b.Run("JSON格式", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			deviceID := fmt.Sprintf("json_device_%d", i)
			err := manager.WriteDeviceDataAsJSON(deviceID, tableFields, deviceData)
			if err != nil {
				b.Fatalf("JSON 格式写入失败: %v", err)
			}
		}
	})

	b.Run("CSV格式", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			deviceID := fmt.Sprintf("csv_device_%d", i)
			err := manager.WriteDeviceDataAsCSV(deviceID, tableFields, deviceData)
			if err != nil {
				b.Fatalf("CSV 格式写入失败: %v", err)
			}
		}
	})
}
