package etcdregister

import (
	"fmt"
	"log"

	"GCF/app/Devicemanager/internal/svc"
)

// ExampleUsage 展示如何使用 DeviceEtcdManager
func ExampleUsage(svcCtx *svc.ServiceContext) {
	// 1. 创建设备 etcd 管理器
	etcdManager := NewDeviceEtcdManager(svcCtx)

	// 2. 注册设备服务
	err := etcdManager.RegisterDeviceService("device-manager", "localhost:8080")
	if err != nil {
		log.Printf("Failed to register service: %v", err)
		return
	}

	// 3. 写入设备数据示例
	deviceID := "device001"
	tableFields := []string{"device_id", "name", "type", "status", "temperature", "humidity"}
	deviceData := map[string]interface{}{
		"device_id":   deviceID,
		"name":        "Temperature Sensor 1",
		"type":        "sensor",
		"status":      "active",
		"temperature": 25.5,
		"humidity":    60.2,
	}

	err = etcdManager.WriteDeviceData(deviceID, tableFields, deviceData)
	if err != nil {
		log.Printf("Failed to write device data: %v", err)
		return
	}

	// 4. 写入设备配置示例
	deviceConfig := map[string]interface{}{
		"sampling_rate": 1000,
		"threshold":     30.0,
		"enabled":       true,
		"location":      "building_a_room_101",
	}

	err = etcdManager.WriteDeviceConfig(deviceID, deviceConfig)
	if err != nil {
		log.Printf("Failed to write device config: %v", err)
		return
	}

	// 5. 更新设备状态示例
	err = etcdManager.WriteDeviceStatus(deviceID, "online")
	if err != nil {
		log.Printf("Failed to write device status: %v", err)
		return
	}

	// 6. 读取设备数据示例
	data, err := etcdManager.ReadDeviceData(deviceID)
	if err != nil {
		log.Printf("Failed to read device data: %v", err)
		return
	}
	fmt.Printf("Device data: %+v\n", data)

	// 7. 批量写入设备数据示例
	batchDevices := map[string]map[string]interface{}{
		"device002": {
			"device_id":   "device002",
			"name":        "Humidity Sensor 1",
			"type":        "sensor",
			"status":      "active",
			"temperature": 22.3,
			"humidity":    55.8,
		},
		"device003": {
			"device_id":   "device003",
			"name":        "Pressure Sensor 1",
			"type":        "sensor",
			"status":      "active",
			"temperature": 24.1,
			"humidity":    58.5,
		},
	}

	err = etcdManager.BatchWriteDeviceData(batchDevices, tableFields)
	if err != nil {
		log.Printf("Failed to batch write device data: %v", err)
		return
	}

	// 8. 监听设备变化示例
	etcdManager.WatchDeviceChanges(deviceID, func(key, value string) {
		fmt.Printf("Device change detected - Key: %s, Value: %s\n", key, value)
	})

	fmt.Println("EtcdManager usage examples completed successfully!")
}

// ExampleWithDifferentFormats 展示不同格式的数据写入
func ExampleWithDifferentFormats(svcCtx *svc.ServiceContext) {
	etcdManager := NewDeviceEtcdManager(svcCtx)
	deviceID := "device004"
	tableFields := []string{"device_id", "temperature", "humidity", "status"}
	deviceData := map[string]interface{}{
		"device_id":   deviceID,
		"temperature": 26.8,
		"humidity":    62.1,
		"status":      "active",
	}

	// JSON 格式
	builder := &DeviceDataBuilder{
		TableFields:    tableFields,
		DeviceData:     deviceData,
		AdditionalData: map[string]interface{}{"format": "json", "version": "1.0"},
		Format:         "json",
	}
	jsonString, _ := etcdManager.BuildDataString(builder)
	fmt.Printf("JSON Format: %s\n", jsonString)

	// KV 格式
	builder.Format = "kv"
	kvString, _ := etcdManager.BuildDataString(builder)
	fmt.Printf("KV Format: %s\n", kvString)

	// CSV 格式
	builder.Format = "csv"
	csvString, _ := etcdManager.BuildDataString(builder)
	fmt.Printf("CSV Format: %s\n", csvString)
}

// ExampleFrameDataProcessing 基于您的 device.proto 处理帧数据
func ExampleFrameDataProcessing(svcCtx *svc.ServiceContext) {
	etcdManager := NewDeviceEtcdManager(svcCtx)

	// 模拟处理 Modbus 帧数据
	deviceID := "modbus_device_001"
	frameData := map[string]interface{}{
		"frame_uid":  "frame_001",
		"frame_type": "modbus",
		"modbus_info": map[string]interface{}{
			"tid":   "0001",
			"pid":   "0000",
			"len":   "0006",
			"uid":   "01",
			"fc":    "03",
			"datas": "000A0002",
		},
		"timestamp": "2024-01-10T10:30:00Z",
		"processed": true,
	}

	tableFields := []string{"frame_uid", "frame_type", "timestamp", "processed"}
	err := etcdManager.WriteDeviceData(deviceID, tableFields, frameData)
	if err != nil {
		log.Printf("Failed to write frame data: %v", err)
		return
	}

	fmt.Printf("Frame data written for device: %s\n", deviceID)
}
