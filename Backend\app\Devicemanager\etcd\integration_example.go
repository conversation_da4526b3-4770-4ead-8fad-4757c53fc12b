package etcdregister

import (
	"context"
	"fmt"
	"log"

	"GCF/app/Devicemanager/internal/config"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"
)

// DeviceDataProcessor 设备数据处理器
type DeviceDataProcessor struct {
	etcdManager *DeviceEtcdManager
	svcCtx      *svc.ServiceContext
}

// NewDeviceDataProcessor 创建设备数据处理器
func NewDeviceDataProcessor(svcCtx *svc.ServiceContext) *DeviceDataProcessor {
	return &DeviceDataProcessor{
		etcdManager: NewDeviceEtcdManager(svcCtx),
		svcCtx:      svcCtx,
	}
}

// ProcessDeviceRegistration 处理设备注册
func (ddp *DeviceDataProcessor) ProcessDeviceRegistration(req *types.RegisterDeviceRequest) error {
	// 1. 写入设备基本信息
	tableFields := []string{"device_id", "name", "type", "location", "status"}
	deviceData := map[string]interface{}{
		"device_id": req.DeviceID,
		"name":      req.Name,
		"type":      req.Type,
		"location":  req.Location,
		"status":    "registered",
	}

	err := ddp.etcdManager.WriteDeviceData(req.DeviceID, tableFields, deviceData)
	if err != nil {
		return fmt.Errorf("failed to write device data: %v", err)
	}

	// 2. 写入设备配置
	deviceConfig := map[string]interface{}{
		"auto_start":     true,
		"sampling_rate":  1000,
		"data_retention": 7, // 天
		"alert_enabled":  true,
	}

	err = ddp.etcdManager.WriteDeviceConfig(req.DeviceID, deviceConfig)
	if err != nil {
		return fmt.Errorf("failed to write device config: %v", err)
	}

	// 3. 设置初始状态
	err = ddp.etcdManager.WriteDeviceStatus(req.DeviceID, "registered")
	if err != nil {
		return fmt.Errorf("failed to write device status: %v", err)
	}

	log.Printf("Device registered successfully: %s", req.DeviceID)
	return nil
}

// ProcessFrameData 处理帧数据（基于您的 device.proto）
func (ddp *DeviceDataProcessor) ProcessFrameData(deviceID string, frameInfo map[string]interface{}) error {
	// 提取数据表字段
	tableFields := []string{
		"device_id", "frame_uid", "frame_type", "protocol_data", 
		"timestamp", "processed", "data_size",
	}

	// 构建设备数据
	deviceData := map[string]interface{}{
		"device_id":     deviceID,
		"frame_uid":     frameInfo["frame_uid"],
		"frame_type":    frameInfo["frame_type"],
		"protocol_data": frameInfo,
		"processed":     true,
		"data_size":     len(fmt.Sprintf("%v", frameInfo)),
	}

	// 写入到 etcd
	err := ddp.etcdManager.WriteDeviceData(deviceID, tableFields, deviceData)
	if err != nil {
		return fmt.Errorf("failed to process frame data: %v", err)
	}

	// 更新设备状态为活跃
	err = ddp.etcdManager.WriteDeviceStatus(deviceID, "active")
	if err != nil {
		log.Printf("Warning: failed to update device status: %v", err)
	}

	return nil
}

// ProcessControlCommand 处理控制命令
func (ddp *DeviceDataProcessor) ProcessControlCommand(req *types.ControlDeviceRequest) error {
	// 记录控制命令到 etcd
	commandData := map[string]interface{}{
		"device_id": req.DeviceID,
		"command":   req.Command,
		"params":    req.Params,
		"operator":  req.Operator,
		"executed":  false,
	}

	tableFields := []string{"device_id", "command", "params", "operator", "executed"}
	
	// 使用特殊的键来存储命令历史
	keyConfig := &EtcdKeyConfig{
		ServicePrefix: "device",
		DeviceID:      req.DeviceID,
		DataType:      "commands",
	}
	
	builder := &DeviceDataBuilder{
		TableFields:    tableFields,
		DeviceData:     commandData,
		AdditionalData: map[string]interface{}{"command_id": fmt.Sprintf("cmd_%d", req.Timestamp)},
		Format:         "json",
	}

	dataString, err := ddp.etcdManager.BuildDataString(builder)
	if err != nil {
		return err
	}

	key := fmt.Sprintf("%s/cmd_%d", ddp.etcdManager.BuildEtcdKey(keyConfig), req.Timestamp)
	_, err = ddp.etcdManager.etcdClient.Put(key, dataString)
	if err != nil {
		return fmt.Errorf("failed to record control command: %v", err)
	}

	return nil
}

// GetDeviceInfo 获取设备完整信息
func (ddp *DeviceDataProcessor) GetDeviceInfo(deviceID string) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 获取设备数据
	deviceData, err := ddp.etcdManager.ReadDeviceData(deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to read device data: %v", err)
	}
	result["data"] = deviceData

	// 获取设备配置
	configKey := ddp.etcdManager.BuildEtcdKey(&EtcdKeyConfig{
		ServicePrefix: "device",
		DeviceID:      deviceID,
		DataType:      "config",
	})
	
	configResp, err := ddp.etcdManager.etcdClient.Get(configKey)
	if err == nil && len(configResp.Kvs) > 0 {
		var config map[string]interface{}
		if json.Unmarshal(configResp.Kvs[0].Value, &config) == nil {
			result["config"] = config
		}
	}

	// 获取设备状态
	statusKey := ddp.etcdManager.BuildEtcdKey(&EtcdKeyConfig{
		ServicePrefix: "device",
		DeviceID:      deviceID,
		DataType:      "status",
	})
	
	statusResp, err := ddp.etcdManager.etcdClient.Get(statusKey)
	if err == nil && len(statusResp.Kvs) > 0 {
		var status map[string]interface{}
		if json.Unmarshal(statusResp.Kvs[0].Value, &status) == nil {
			result["status"] = status
		}
	}

	return result, nil
}

// StartDeviceMonitoring 启动设备监控
func (ddp *DeviceDataProcessor) StartDeviceMonitoring(deviceID string) {
	ddp.etcdManager.WatchDeviceChanges(deviceID, func(key, value string) {
		log.Printf("Device %s changed - Key: %s", deviceID, key)
		
		// 可以在这里添加业务逻辑，比如：
		// - 发送通知
		// - 更新缓存
		// - 触发其他服务
		
		// 示例：如果是状态变化，记录日志
		if fmt.Sprintf("device/%s/status", deviceID) == key {
			log.Printf("Device %s status changed to: %s", deviceID, value)
		}
	})
}

// InitializeEtcdArchitecture 初始化 etcd 架构
func InitializeEtcdArchitecture(c config.Config) (*DeviceDataProcessor, error) {
	// 创建服务上下文
	svcCtx := svc.NewServiceContext(c)
	
	// 创建数据处理器
	processor := NewDeviceDataProcessor(svcCtx)
	
	// 注册设备管理服务
	err := processor.etcdManager.RegisterDeviceService("device-manager", fmt.Sprintf("localhost:%d", c.Port))
	if err != nil {
		return nil, fmt.Errorf("failed to register device service: %v", err)
	}
	
	log.Println("Etcd architecture initialized successfully")
	return processor, nil
}
