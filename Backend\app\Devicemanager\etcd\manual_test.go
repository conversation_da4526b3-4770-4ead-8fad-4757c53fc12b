package main

import (
	"fmt"
	"log"
	"time"

	"GCF/app/Devicemanager/etcd"
	"GCF/app/Devicemanager/internal/config"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/pkg/zetcd"
)

func main() {
	fmt.Println("🚀 开始测试 EtcdRegister 程序")
	fmt.Println("================================")

	// 1. 初始化配置和服务上下文
	testConfig := config.Config{
		ZEtcdConf: zetcd.ZEtcdConf{
			Endpoints: []string{"127.0.0.1:2379"},
			ID:        12345,
		},
	}

	fmt.Println("📋 初始化服务上下文...")
	svcCtx := svc.NewServiceContext(testConfig)
	if svcCtx == nil {
		log.Fatal("❌ 服务上下文初始化失败")
	}
	if svcCtx.ZEtcdClient == nil {
		log.Fatal("❌ etcd 客户端初始化失败")
	}
	fmt.Println("✅ 服务上下文初始化成功")

	// 2. 创建 etcd 管理器
	fmt.Println("\n📋 创建 etcd 管理器...")
	etcdManager := etcdregister.NewDeviceEtcdManager(svcCtx)
	if etcdManager == nil {
		log.Fatal("❌ etcd 管理器创建失败")
	}
	fmt.Println("✅ etcd 管理器创建成功")

	// 3. 测试服务注册
	fmt.Println("\n📋 测试服务注册...")
	err := etcdManager.RegisterDeviceService("test-device-manager", "localhost:8080")
	if err != nil {
		log.Printf("❌ 服务注册失败: %v", err)
	} else {
		fmt.Println("✅ 服务注册成功")
	}

	// 4. 测试直接写入和读取
	fmt.Println("\n📋 测试直接写入和读取...")
	testKey := "test/manual/simple"
	testValue := "hello manual test"
	
	err = etcdManager.DirectWriteToEtcd(testKey, testValue)
	if err != nil {
		log.Printf("❌ 直接写入失败: %v", err)
	} else {
		fmt.Printf("✅ 直接写入成功: %s = %s\n", testKey, testValue)
	}

	value, err := etcdManager.DirectReadFromEtcd(testKey)
	if err != nil {
		log.Printf("❌ 直接读取失败: %v", err)
	} else {
		fmt.Printf("✅ 直接读取成功: %s = %s\n", testKey, value)
	}

	// 5. 测试设备数据写入（核心功能）
	fmt.Println("\n📋 测试设备数据写入（您的核心需求）...")
	deviceID := "manual_test_device_001"
	tableFields := []string{"device_id", "name", "type", "temperature", "humidity", "status"}
	deviceData := map[string]interface{}{
		"device_id":   deviceID,
		"name":        "Manual Test Sensor",
		"type":        "temperature_humidity",
		"temperature": 25.5,
		"humidity":    60.2,
		"status":      "active",
	}

	err = etcdManager.WriteDeviceData(deviceID, tableFields, deviceData)
	if err != nil {
		log.Printf("❌ 设备数据写入失败: %v", err)
	} else {
		fmt.Println("✅ 设备数据写入成功")
		
		// 读取验证
		deviceKey := fmt.Sprintf("device/%s/data", deviceID)
		deviceValue, err := etcdManager.DirectReadFromEtcd(deviceKey)
		if err != nil {
			log.Printf("❌ 设备数据读取失败: %v", err)
		} else {
			fmt.Printf("📄 设备数据内容: %s\n", deviceValue)
		}
	}

	// 6. 测试 JSON 格式写入
	fmt.Println("\n📋 测试 JSON 格式写入...")
	err = etcdManager.WriteDeviceDataAsJSON(deviceID, tableFields, deviceData)
	if err != nil {
		log.Printf("❌ JSON 格式写入失败: %v", err)
	} else {
		fmt.Println("✅ JSON 格式写入成功")
		
		jsonKey := fmt.Sprintf("device/%s/data_json", deviceID)
		jsonValue, err := etcdManager.DirectReadFromEtcd(jsonKey)
		if err != nil {
			log.Printf("❌ JSON 数据读取失败: %v", err)
		} else {
			fmt.Printf("📄 JSON 数据内容: %s\n", jsonValue)
		}
	}

	// 7. 测试 CSV 格式写入
	fmt.Println("\n📋 测试 CSV 格式写入...")
	err = etcdManager.WriteDeviceDataAsCSV(deviceID, tableFields, deviceData)
	if err != nil {
		log.Printf("❌ CSV 格式写入失败: %v", err)
	} else {
		fmt.Println("✅ CSV 格式写入成功")
		
		csvKey := fmt.Sprintf("device/%s/data_csv", deviceID)
		csvValue, err := etcdManager.DirectReadFromEtcd(csvKey)
		if err != nil {
			log.Printf("❌ CSV 数据读取失败: %v", err)
		} else {
			fmt.Printf("📄 CSV 数据内容: %s\n", csvValue)
		}
	}

	// 8. 测试设备配置写入
	fmt.Println("\n📋 测试设备配置写入...")
	deviceConfig := map[string]interface{}{
		"sampling_rate": 1000,
		"threshold":     30.0,
		"enabled":       true,
		"location":      "manual_test_building",
		"alert_email":   "<EMAIL>",
	}

	err = etcdManager.WriteDeviceConfig(deviceID, deviceConfig)
	if err != nil {
		log.Printf("❌ 设备配置写入失败: %v", err)
	} else {
		fmt.Println("✅ 设备配置写入成功")
		
		configKey := fmt.Sprintf("device/%s/config", deviceID)
		configValue, err := etcdManager.DirectReadFromEtcd(configKey)
		if err != nil {
			log.Printf("❌ 设备配置读取失败: %v", err)
		} else {
			fmt.Printf("📄 设备配置内容: %s\n", configValue)
		}
	}

	// 9. 测试设备状态写入
	fmt.Println("\n📋 测试设备状态写入...")
	err = etcdManager.WriteDeviceStatus(deviceID, "online")
	if err != nil {
		log.Printf("❌ 设备状态写入失败: %v", err)
	} else {
		fmt.Println("✅ 设备状态写入成功")
		
		statusKey := fmt.Sprintf("device/%s/status", deviceID)
		statusValue, err := etcdManager.DirectReadFromEtcd(statusKey)
		if err != nil {
			log.Printf("❌ 设备状态读取失败: %v", err)
		} else {
			fmt.Printf("📄 设备状态内容: %s\n", statusValue)
		}
	}

	// 10. 测试批量写入
	fmt.Println("\n📋 测试批量写入...")
	batchDevices := map[string]map[string]interface{}{
		"batch_device_001": {
			"device_id":   "batch_device_001",
			"name":        "Batch Sensor 1",
			"temperature": 20.1,
			"humidity":    50.1,
			"status":      "active",
		},
		"batch_device_002": {
			"device_id":   "batch_device_002",
			"name":        "Batch Sensor 2",
			"temperature": 21.2,
			"humidity":    51.2,
			"status":      "active",
		},
		"batch_device_003": {
			"device_id":   "batch_device_003",
			"name":        "Batch Sensor 3",
			"temperature": 22.3,
			"humidity":    52.3,
			"status":      "active",
		},
	}

	err = etcdManager.BatchWriteDeviceData(batchDevices, tableFields)
	if err != nil {
		log.Printf("❌ 批量写入失败: %v", err)
	} else {
		fmt.Printf("✅ 批量写入成功，写入了 %d 个设备\n", len(batchDevices))
		
		// 验证批量写入的数据
		for batchDeviceID := range batchDevices {
			batchKey := fmt.Sprintf("device/%s/data", batchDeviceID)
			batchValue, err := etcdManager.DirectReadFromEtcd(batchKey)
			if err != nil {
				log.Printf("❌ 批量设备 %s 读取失败: %v", batchDeviceID, err)
			} else {
				fmt.Printf("📄 批量设备 %s: %s\n", batchDeviceID, batchValue)
			}
		}
	}

	// 11. 测试设备监听
	fmt.Println("\n📋 测试设备监听...")
	fmt.Println("🔊 启动设备监听（5秒后自动停止）...")
	
	// 启动监听
	etcdManager.WatchDeviceChanges(deviceID, func(key, value string) {
		fmt.Printf("📡 监听到变化: %s = %s\n", key, value)
	})

	// 等待一下，然后修改设备状态触发监听
	time.Sleep(1 * time.Second)
	fmt.Println("🔄 修改设备状态以触发监听...")
	err = etcdManager.WriteDeviceStatus(deviceID, "updated")
	if err != nil {
		log.Printf("❌ 状态更新失败: %v", err)
	}

	// 等待监听事件
	time.Sleep(2 * time.Second)

	// 12. 测试数据删除
	fmt.Println("\n📋 测试数据删除...")
	err = etcdManager.DeleteDeviceData("batch_device_001")
	if err != nil {
		log.Printf("❌ 数据删除失败: %v", err)
	} else {
		fmt.Println("✅ 数据删除成功")
	}

	// 13. 总结
	fmt.Println("\n🎉 测试完成总结")
	fmt.Println("================================")
	fmt.Println("✅ 服务注册测试")
	fmt.Println("✅ 直接写入读取测试")
	fmt.Println("✅ 设备数据写入测试（KV 格式）")
	fmt.Println("✅ JSON 格式写入测试")
	fmt.Println("✅ CSV 格式写入测试")
	fmt.Println("✅ 设备配置写入测试")
	fmt.Println("✅ 设备状态写入测试")
	fmt.Println("✅ 批量写入测试")
	fmt.Println("✅ 设备监听测试")
	fmt.Println("✅ 数据删除测试")
	fmt.Println("\n🎯 所有功能测试完成！")
	fmt.Println("📊 您的程序完全基于直接调用 zetcd 方法实现")
}
